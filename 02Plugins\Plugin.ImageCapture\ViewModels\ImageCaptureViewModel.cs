using EventMgrLib;
using HalconDotNet;
using Plugin.ImageCapture.Views;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using VM.Halcon;
using VM.Halcon.Config;
using VM.Start.Attributes;
using VM.Start.Common;
using VM.Start.Common.Enums;
using VM.Start.Common.Helper;
using VM.Start.Common.Provide;
using VM.Start.Core;
using VM.Start.Events;
using VM.Start.Models;
using VM.Start.ViewModels;
using VM.Start.Views.Dock;

namespace Plugin.ImageCapture.ViewModels
{
    #region enum
    public enum eLinkCommand
    {
        SingleFilePathLink,
        BatchFolderPathLink,
    }
    #endregion

    /// <summary>
    /// 图像采集插件 - 专用于图像文件采集
    /// 特点：单文件采集、批量文件夹采集、支持多种图像格式
    /// 功能：文件选择、图像加载、格式验证、索引导航
    /// </summary>
    [Category("图像处理")]
    [DisplayName("图像采集2")]
    [ModuleImageName("ImageCapture")]
    [Serializable]
    public class ImageCaptureViewModel : ModuleBase
    {
        #region 核心方法

        /// <summary>
        /// 设置默认链接
        /// </summary>
        public override void SetDefaultLink()
        {
            // 图像采集插件不需要输入链接，直接从文件加载
        }

        /// <summary>
        /// 执行模块主要逻辑
        /// </summary>
        /// <returns>执行是否成功</returns>
        public override bool ExeModule()
        {
            Stopwatch.Restart();
            try
            {
                // 检查是否有有效的图像
                if (CurrentImage == null || !CurrentImage.IsInitialized())
                {
                    Logger.AddLog("没有加载有效的图像", eMsgType.Warn);
                    ChangeModuleRunStatus(eRunStatus.NG);
                    return false;
                }

                // 检查文件路径
                if (string.IsNullOrEmpty(CurrentFilePath))
                {
                    Logger.AddLog("当前文件路径为空", eMsgType.Warn);
                    ChangeModuleRunStatus(eRunStatus.NG);
                    return false;
                }

                // 设置显示图像
                DispImage = new VM.Halcon.Config.RImage(CurrentImage);

                // 显示图像
                DisplayCurrentImage();

                Logger.AddLog($"图像采集成功: {System.IO.Path.GetFileName(CurrentFilePath)}", eMsgType.Info);
                ChangeModuleRunStatus(eRunStatus.OK);
                return true;
            }
            catch (Exception ex)
            {
                ChangeModuleRunStatus(eRunStatus.NG);
                Logger.GetExceptionMsg(ex);
                return false;
            }
        }

        /// <summary>
        /// 添加输出参数
        /// </summary>
        public override void AddOutputParams()
        {
            base.AddOutputParams();
            AddOutputParam("当前图像", "HImage", CurrentImage);
            AddOutputParam("文件路径", "string", CurrentFilePath);
            AddOutputParam("文件数量", "int", FileCount);
            AddOutputParam("当前索引", "int", CurrentIndex);
            AddOutputParam("状态", "bool", ModuleParam.Status == eRunStatus.OK);
            AddOutputParam("执行时间", "int", ModuleParam.ElapsedTime);
        }

        #endregion

        #region 属性定义

        /// <summary>
        /// 当前图像
        /// </summary>
        private HImage _CurrentImage;
        public HImage CurrentImage
        {
            get { return _CurrentImage; }
            set { Set(ref _CurrentImage, value); }
        }

        /// <summary>
        /// 当前文件路径
        /// </summary>
        private string _CurrentFilePath = "";
        public string CurrentFilePath
        {
            get { return _CurrentFilePath; }
            set { Set(ref _CurrentFilePath, value); }
        }

        /// <summary>
        /// 文件数量
        /// </summary>
        private int _FileCount = 0;
        public int FileCount
        {
            get { return _FileCount; }
            set { Set(ref _FileCount, value); }
        }

        /// <summary>
        /// 当前索引
        /// </summary>
        private int _CurrentIndex = 0;
        public int CurrentIndex
        {
            get { return _CurrentIndex; }
            set { Set(ref _CurrentIndex, value); }
        }

        #region 单文件采集属性

        /// <summary>
        /// 单文件路径链接文本
        /// </summary>
        private string _SingleFilePathLinkText = "";
        public string SingleFilePathLinkText
        {
            get { return _SingleFilePathLinkText; }
            set
            {
                Set(ref _SingleFilePathLinkText, value);
                if (!string.IsNullOrEmpty(value) && !value.StartsWith("&"))
                {
                    LoadSingleImageFile(value);
                }
            }
        }

        /// <summary>
        /// 支持的图像格式
        /// </summary>
        private string _SupportedFormats = "jpg, png, bmp, tiff, jpeg";
        public string SupportedFormats
        {
            get { return _SupportedFormats; }
            set { Set(ref _SupportedFormats, value); }
        }

		private string _WindowKey = "";
		public string WindowKey
		{
			get { return _WindowKey; }
			set { Set(ref _WindowKey, value); }
		}
		/// <summary>
		/// 单文件图像信息
		/// </summary>
		private string _SingleFileImageInfo = "";
        public string SingleFileImageInfo
        {
            get { return _SingleFileImageInfo; }
            set { Set(ref _SingleFileImageInfo, value); }
        }

        #endregion

        #region 批量采集属性

        /// <summary>
        /// 批量文件夹路径链接文本
        /// </summary>
        private string _BatchFolderPathLinkText = "";
        public string BatchFolderPathLinkText
        {
            get { return _BatchFolderPathLinkText; }
            set
            {
                Set(ref _BatchFolderPathLinkText, value);
                if (!string.IsNullOrEmpty(value) && !value.StartsWith("&"))
                {
                    ScanFolderImages(value);
                }
            }
        }

        /// <summary>
        /// 图像文件列表
        /// </summary>
        private System.Collections.Generic.List<string> _ImageFileList = new System.Collections.Generic.List<string>();
        public System.Collections.Generic.List<string> ImageFileList
        {
            get { return _ImageFileList; }
            set { Set(ref _ImageFileList, value); }
        }

        /// <summary>
        /// 批量模式文件信息
        /// </summary>
        private string _BatchFileInfo = "";
        public string BatchFileInfo
        {
            get { return _BatchFileInfo; }
            set { Set(ref _BatchFileInfo, value); }
        }

        /// <summary>
        /// 是否可以上一张
        /// </summary>
        public bool CanGoPrevious => CurrentIndex > 0 && FileCount > 0;

        /// <summary>
        /// 是否可以下一张
        /// </summary>
        public bool CanGoNext => CurrentIndex < FileCount - 1 && FileCount > 0;

        #endregion

        #endregion

        #region 命令定义

        /// <summary>
        /// Load
        /// </summary>
        public override void Loaded()
        {
            base.Loaded();
            var view = ModuleView as ImageCaptureView;
            if (view != null)
            {
                ClosedView = true;
                if (view.mWindowH == null)
                {
                    view.mWindowH = new VMHWindowControl();
                    //view.winFormHost.Child = view.mWindowH;
                }
            }
        }

        /// <summary>
        /// 统一链接命令
        /// </summary>
        [NonSerialized]
        private CommandBase _LinkCommand;
        public CommandBase LinkCommand
        {
            get
            {
                if (_LinkCommand == null)
                {
                    //以GUID+类名作为筛选器
                    EventMgr.Ins.GetEvent<VarChangedEvent>().Subscribe(OnVarChanged, o => o.SendName.StartsWith($"{ModuleGuid}"));
                    _LinkCommand = new CommandBase((obj) =>
                    {
                        eLinkCommand linkCommand = (eLinkCommand)obj;
                        switch (linkCommand)
                        {
                            case eLinkCommand.SingleFilePathLink:
                                CommonMethods.GetModuleList(ModuleParam, VarLinkViewModel.Ins.Modules, "string");
                                EventMgr.Ins.GetEvent<OpenVarLinkViewEvent>().Publish($"{ModuleGuid},SingleFilePathLink");
                                break;
                            case eLinkCommand.BatchFolderPathLink:
                                CommonMethods.GetModuleList(ModuleParam, VarLinkViewModel.Ins.Modules, "string");
                                EventMgr.Ins.GetEvent<OpenVarLinkViewEvent>().Publish($"{ModuleGuid},BatchFolderPathLink");
                                break;
                            default:
                                break;
                        }
                    });
                }
                return _LinkCommand;
            }
        }

        /// <summary>
        /// 上一张命令
        /// </summary>
        [NonSerialized]
        private CommandBase _PreviousCommand;
        public CommandBase PreviousCommand
        {
            get
            {
                if (_PreviousCommand == null)
                {
                    _PreviousCommand = new CommandBase((obj) =>
                    {
                        if (CanGoPrevious)
                        {
                            LoadImageByIndex(CurrentIndex - 1);
                            RaisePropertyChanged(nameof(CanGoPrevious));
                            RaisePropertyChanged(nameof(CanGoNext));
                        }
                    });
                }
                return _PreviousCommand;
            }
        }

        /// <summary>
        /// 下一张命令
        /// </summary>
        [NonSerialized]
        private CommandBase _NextCommand;
        public CommandBase NextCommand
        {
            get
            {
                if (_NextCommand == null)
                {
                    _NextCommand = new CommandBase((obj) =>
                    {
                        if (CanGoNext)
                        {
                            LoadImageByIndex(CurrentIndex + 1);
                            RaisePropertyChanged(nameof(CanGoPrevious));
                            RaisePropertyChanged(nameof(CanGoNext));
                        }
                    });
                }
                return _NextCommand;
            }
        }

        /// <summary>
        /// 选择单文件命令
        /// </summary>
        [NonSerialized]
        private CommandBase _SelectSingleFileCommand;
        public CommandBase SelectSingleFileCommand
        {
            get
            {
                if (_SelectSingleFileCommand == null)
                {
                    _SelectSingleFileCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            var dialog = new Microsoft.Win32.OpenFileDialog
                            {
                                Title = "选择图像文件",
                                Filter = "图像文件|*.jpg;*.jpeg;*.png;*.bmp;*.tiff;*.tif|所有文件|*.*",
                                FilterIndex = 1
                            };

                            if (dialog.ShowDialog() == true)
                            {
                                SingleFilePathLinkText = dialog.FileName;
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"选择文件失败: {ex.Message}", eMsgType.Error);
                        }
                    });
                }
                return _SelectSingleFileCommand;
            }
        }

        /// <summary>
        /// 重新加载单文件命令
        /// </summary>
        [NonSerialized]
        private CommandBase _ReloadSingleFileCommand;
        public CommandBase ReloadSingleFileCommand
        {
            get
            {
                if (_ReloadSingleFileCommand == null)
                {
                    _ReloadSingleFileCommand = new CommandBase((obj) =>
                    {
                        if (!string.IsNullOrEmpty(SingleFilePathLinkText))
                        {
                            LoadSingleImageFile(SingleFilePathLinkText);
                        }
                    });
                }
                return _ReloadSingleFileCommand;
            }
        }

        /// <summary>
        /// 选择文件夹命令
        /// </summary>
        [NonSerialized]
        private CommandBase _SelectFolderCommand;
        public CommandBase SelectFolderCommand
        {
            get
            {
                if (_SelectFolderCommand == null)
                {
                    _SelectFolderCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            using (var dialog = new System.Windows.Forms.FolderBrowserDialog())
                            {
                                dialog.Description = "选择包含图像文件的文件夹";
                                dialog.ShowNewFolderButton = false;

                                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                                {
                                    BatchFolderPathLinkText = dialog.SelectedPath;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"选择文件夹失败: {ex.Message}", eMsgType.Error);
                        }
                    });
                }
                return _SelectFolderCommand;
            }
        }

        /// <summary>
        /// 变量变化事件处理
        /// </summary>
        /// <param name="obj"></param>
        private void OnVarChanged(VarChangedEventParamModel obj)
        {
            switch (obj.SendName.Split(',')[1])
            {
                case "SingleFilePathLink":
                    SingleFilePathLinkText = obj.LinkName;
                    break;
                case "BatchFolderPathLink":
                    BatchFolderPathLinkText = obj.LinkName;
                    break;
                default:
                    break;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载单个图像文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        private void LoadSingleImageFile(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                {
                    Logger.AddLog($"文件不存在: {filePath}", eMsgType.Warn);
                    return;
                }

                // 验证文件格式
                if (!IsValidImageFormat(filePath))
                {
                    Logger.AddLog($"不支持的图像格式: {filePath}", eMsgType.Warn);
                    return;
                }

                // 加载图像
                var image = new HImage(filePath);
                if (image != null && image.IsInitialized())
                {
                    CurrentImage = image;
                    CurrentFilePath = filePath;
                    FileCount = 1;
                    CurrentIndex = 0;

                    // 更新图像信息
                    UpdateSingleFileImageInfo(image, filePath);

                    // 显示图像
                    DisplayCurrentImage();

                    Logger.AddLog($"成功加载图像: {System.IO.Path.GetFileName(filePath)}", eMsgType.Info);
                }
                else
                {
                    Logger.AddLog($"图像加载失败: {filePath}", eMsgType.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.AddLog($"加载图像异常: {ex.Message}", eMsgType.Error);
            }
        }

        /// <summary>
        /// 验证图像格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否为支持的格式</returns>
        private bool IsValidImageFormat(string filePath)
        {
            var extension = System.IO.Path.GetExtension(filePath).ToLower();
            var supportedExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif" };
            return supportedExtensions.Contains(extension);
        }

        /// <summary>
        /// 更新单文件图像信息
        /// </summary>
        /// <param name="image">图像对象</param>
        /// <param name="filePath">文件路径</param>
        private void UpdateSingleFileImageInfo(HImage image, string filePath)
        {
            try
            {
                var fileInfo = new System.IO.FileInfo(filePath);
                var fileName = fileInfo.Name;
                var fileSize = (fileInfo.Length / 1024.0).ToString("F1") + " KB";

                HTuple width, height, channels;
                image.GetImageSize(out width, out height);
                HOperatorSet.CountChannels(image, out channels);

                SingleFileImageInfo = $"文件: {fileName}\n" +
                                    $"尺寸: {width} × {height}\n" +
                                    $"通道: {channels}\n" +
                                    $"大小: {fileSize}";
            }
            catch (Exception ex)
            {
                SingleFileImageInfo = $"获取图像信息失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 显示当前图像
        /// </summary>
        private void DisplayCurrentImage()
        {
            try
            {
                var view = ModuleView as ImageCaptureView;
                if (view?.mWindowH != null && CurrentImage != null && CurrentImage.IsInitialized())
                {
                    view.mWindowH.HobjectToHimage(CurrentImage);
					WindowKey= view.mWindowH.Image.GetType().Name;

				}
            }
            catch (Exception ex)
            {
                Logger.AddLog($"显示图像失败: {ex.Message}", eMsgType.Error);
            }
        }

        /// <summary>
        /// 扫描文件夹中的图像文件
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        private void ScanFolderImages(string folderPath)
        {
            try
            {
                if (string.IsNullOrEmpty(folderPath) || !System.IO.Directory.Exists(folderPath))
                {
                    Logger.AddLog($"文件夹不存在: {folderPath}", eMsgType.Warn);
                    return;
                }

                var supportedExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif" };
                var imageFiles = System.IO.Directory.GetFiles(folderPath)
                    .Where(file => supportedExtensions.Contains(System.IO.Path.GetExtension(file).ToLower()))
                    .OrderBy(file => file)
                    .ToList();

                ImageFileList = imageFiles;
                FileCount = imageFiles.Count;
                CurrentIndex = 0;

                if (FileCount > 0)
                {
                    LoadImageByIndex(0);
                    UpdateBatchFileInfo();
                    Logger.AddLog($"扫描完成，找到 {FileCount} 个图像文件", eMsgType.Info);
                }
                else
                {
                    BatchFileInfo = "未找到图像文件";
                    Logger.AddLog("文件夹中未找到支持的图像文件", eMsgType.Warn);
                }

                // 通知导航按钮状态变化
                RaisePropertyChanged(nameof(CanGoPrevious));
                RaisePropertyChanged(nameof(CanGoNext));
            }
            catch (Exception ex)
            {
                Logger.AddLog($"扫描文件夹异常: {ex.Message}", eMsgType.Error);
            }
        }

        /// <summary>
        /// 根据索引加载图像
        /// </summary>
        /// <param name="index">索引</param>
        private void LoadImageByIndex(int index)
        {
            try
            {
                if (index < 0 || index >= FileCount || ImageFileList == null)
                {
                    return;
                }

                var filePath = ImageFileList[index];
                var image = new HImage(filePath);

                if (image != null && image.IsInitialized())
                {
                    CurrentImage = image;
                    CurrentFilePath = filePath;
                    CurrentIndex = index;

                    DisplayCurrentImage();
                    UpdateBatchFileInfo();

                    Logger.AddLog($"加载图像 [{index + 1}/{FileCount}]: {System.IO.Path.GetFileName(filePath)}", eMsgType.Info);
                }
                else
                {
                    Logger.AddLog($"图像加载失败: {filePath}", eMsgType.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.AddLog($"加载图像异常: {ex.Message}", eMsgType.Error);
            }
        }

        /// <summary>
        /// 更新批量文件信息
        /// </summary>
        private void UpdateBatchFileInfo()
        {
            try
            {
                if (FileCount == 0)
                {
                    BatchFileInfo = "无图像文件";
                    return;
                }

                var fileName = System.IO.Path.GetFileName(CurrentFilePath);
                BatchFileInfo = $"文件: {fileName}\n" +
                              $"索引: {CurrentIndex + 1} / {FileCount}\n" +
                              $"路径: {CurrentFilePath}";

                if (CurrentImage != null && CurrentImage.IsInitialized())
                {
                    HTuple width, height;
                    CurrentImage.GetImageSize(out width, out height);
                    BatchFileInfo += $"\n尺寸: {width} × {height}";
                }
            }
            catch (Exception ex)
            {
                BatchFileInfo = $"获取文件信息失败: {ex.Message}";
            }
        }

        #endregion
    }
}
